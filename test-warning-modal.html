<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警弹框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .warning-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .warning-type {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .plan-detail {
            display: inline-block;
            margin: 2px 4px;
            padding: 2px 8px;
            background: #f0f0f0;
            border-radius: 3px;
            font-size: 12px;
        }
        .plan-no {
            background: #e6f7ff;
            color: #1890ff;
        }
        .contract-no {
            background: #f6ffed;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>预警信息弹框效果预览</h2>
        <p>这是根据您的需求设计的预警弹框效果预览：</p>
        
        <div class="warning-item">
            <div class="warning-type">预计收款</div>
            <div>
                <span class="plan-detail plan-no">PLAN001</span>
                <span class="plan-detail contract-no">CONTRACT001</span>
                <span class="plan-detail plan-no">PLAN002</span>
                <span class="plan-detail contract-no">CONTRACT002</span>
            </div>
        </div>
        
        <div class="warning-item">
            <div class="warning-type">预计付款</div>
            <div>
                <span class="plan-detail plan-no">PLAN003</span>
                <span class="plan-detail contract-no">CONTRACT003</span>
            </div>
        </div>
        
        <div class="warning-item">
            <div class="warning-type">预计许可证申办</div>
            <div>
                <span class="plan-detail plan-no">PLAN004</span>
                <span class="plan-detail contract-no">CONTRACT004</span>
                <span class="plan-detail plan-no">PLAN005</span>
                <span class="plan-detail contract-no">CONTRACT005</span>
                <span class="plan-detail plan-no">PLAN006</span>
                <span class="plan-detail contract-no">CONTRACT006</span>
            </div>
        </div>
        
        <h3>功能说明：</h3>
        <ul>
            <li>页面进入时自动调用 <code>getWaringPlanList</code> 接口</li>
            <li>如果 <code>hasWarningPlans</code> 为 "1"，则显示预警弹框</li>
            <li>如果 <code>hasWarningPlans</code> 为 "0"，则不显示弹框</li>
            <li>弹框显示各种预警类型及对应的计划号和合同号</li>
            <li>弹框尺寸为 800px 宽度，比原来的预警设置弹框更大</li>
        </ul>
    </div>
</body>
</html>
