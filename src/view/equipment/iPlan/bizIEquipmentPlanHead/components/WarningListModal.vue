<template>
  <a-modal
    v-model:visible="props.visible"
    title="预警信息"
    width="800px"
    :maskClosable="false"
    @cancel="handleCancel"
    :keyboard="false"
    :footer="null"
  >
    <div class="warning-list-container">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        size="small"
        bordered
        :scroll="{ y: 400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'warningType'">
            <span class="warning-type-label">{{ record.warningType }}</span>
          </template>
          <template v-if="column.key === 'planDetails'">
            <div class="plan-details">
              <div
                v-for="(item, index) in record.planDetails"
                :key="index"
                class="plan-item"
              >
                <a-tag color="blue" class="plan-tag">
                  {{ item.planNo }}
                </a-tag>
                <a-tag color="green" class="contract-tag">
                  {{ item.contractNo }}
                </a-tag>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <div style="text-align: center; margin-top: 24px;">
      <a-button type="primary" @click="handleCancel">
        确定
      </a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  warningData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 表格列定义
const columns = [
  {
    title: '预警类型',
    key: 'warningType',
    dataIndex: 'warningType',
    width: 200,
    align: 'center'
  },
  {
    title: '相关计划',
    key: 'planDetails',
    dataIndex: 'planDetails',
    align: 'left'
  }
];

// 预警类型映射
const warningTypeMap = {
  receiveWaringPlan: '预计收款',
  paymentWaringPlan: '预计付款',
  arbitrationWaringPlan: '预计裁定',
  licenseWaringPlan: '预计许可证申办',
  transportWaringPlan: '预计准运证申办',
  insuranceWaringPlan: '预计保险申办'
};

// 计算表格数据
const tableData = computed(() => {
  const data = [];
  if (!props.warningData) return data;

  Object.keys(warningTypeMap).forEach(key => {
    const planList = props.warningData[key];
    if (planList && planList.length > 0) {
      data.push({
        key: key,
        warningType: warningTypeMap[key],
        planDetails: planList
      });
    }
  });

  return data;
});

// 处理取消按钮
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.warning-list-container {
  .warning-type-label {
    font-weight: 600;
    color: #1890ff;
  }

  .plan-details {
    .plan-item {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .plan-tag {
        margin-right: 0;
        font-size: 12px;
      }

      .contract-tag {
        margin-right: 0;
        font-size: 12px;
      }
    }
  }
}

:deep(.ant-table-tbody > tr > td) {
  vertical-align: top;
}

:deep(.ant-table-cell) {
  padding: 12px 8px !important;
}
</style>
